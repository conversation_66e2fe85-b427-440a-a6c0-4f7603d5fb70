// TODO: use a connection pool for the database.
// TODO: implement database actions in tickets.
// TODO: try fixing other issues such as sending messages and responding to interactions correctly.

mod create_ticket_channel;
pub mod models;
pub mod schema;

pub use create_ticket_channel::*;

use diesel::SqliteConnection;
use once_cell::sync::Lazy;
use std::collections::HashMap;
use std::sync::RwLock;

use models::{Ticket, TicketChangeset};

use crate::{models::Snowflake, tickets::models::TicketInsert};

pub struct Tickets {
    tickets: RwLock<HashMap<Snowflake, Ticket>>,
}

impl Tickets {
    pub fn new() -> Self {
        Self {
            tickets: RwLock::new(HashMap::new()),
        }
    }

    /// fetches a ticket by user id snowflake.
    pub fn fetch(&self, conn: &mut SqliteConnection, id: Snowflake) -> Option<Ticket> {
        if let Ok(tickets) = self.tickets.read() {
            tickets.get(&id).cloned()
        } else {
            None
        }
    }

    pub fn insert(&self, conn: &mut SqliteConnection, values: TicketInsert) {
        if let Ok(mut tickets) = self.tickets.write() {
            tickets.insert(
                values.user_id,
                Ticket {
                    id: 0,
                    user_id: values.user_id,
                    guild_id: values.guild_id,
                    category_id: values.category_id,
                    channel_id: values.channel_id,
                    contact_id: values.contact_id,
                    contact_name: values.contact_name,
                },
            );
        }
    }

    pub fn update(
        &self,
        conn: &mut SqliteConnection,
        id: Snowflake,
        update: TicketChangeset,
    ) -> Option<Ticket> {
        if let Ok(mut tickets) = self.tickets.write() {
            if let Some(ticket) = tickets.get_mut(&id) {
                if let Some(guild_id) = update.guild_id {
                    ticket.guild_id = guild_id;
                }

                if let Some(category_id) = update.category_id {
                    ticket.category_id = category_id;
                }

                if let Some(channel_id) = update.channel_id {
                    ticket.channel_id = channel_id;
                }

                if let Some(contact_name) = update.contact_name {
                    ticket.contact_name = contact_name;
                }

                return Some(ticket.clone());
            }
        }

        None
    }
}

pub static TICKETS: Lazy<Tickets> = Lazy::new(Tickets::new);
