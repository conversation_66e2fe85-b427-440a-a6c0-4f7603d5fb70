-- Your SQL goes here
CREATE TABLE `tickets` (
	`id` integer PRIMARY KEY NOT NULL,
	`user_id` text NOT NULL,
	`guild_id` text NOT NULL,
	`category_id` text NOT NULL,
	`channel_id` text NOT NULL,
	`contact_id` text NOT NULL,
	`contact_name` text NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `tickets_user_id_unique` ON `tickets` (`user_id`);--> statement-breakpoint
CREATE UNIQUE INDEX `tickets_guild_id_unique` ON `tickets` (`guild_id`);--> statement-breakpoint
CREATE UNIQUE INDEX `tickets_category_id_unique` ON `tickets` (`category_id`);--> statement-breakpoint
CREATE UNIQUE INDEX `tickets_channel_id_unique` ON `tickets` (`channel_id`);--> statement-breakpoint
CREATE UNIQUE INDEX `tickets_contact_id_unique` ON `tickets` (`contact_id`);