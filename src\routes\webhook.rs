use std::time::Duration;

use rocket::{State, futures::future::join_all, post, serde::json::<PERSON><PERSON>};
use serde::Deserialize;
use serenity::all::{ChannelId, CreateAttachment, CreateMessage, GuildId};
use tracing::{error, info, warn};

use crate::{
    ENV,
    state::RocketState,
    tickets::{self, TICKETS, models::TicketChangeset},
    trengo::{self},
};

#[derive(Debug, Deserialize)]
pub struct WebhookPayload<'a> {
    message_id: &'a str,
    ticket_id: &'a str,
    contact_identifier: Option<&'a str>,
}

#[post("/webhook", data = "<payload>")]
pub async fn handler<'a>(payload: Json<WebhookPayload<'a>>, state: &State<RocketState>) {
    info!("recv /webhook with payload: {:?}", payload);

    if let Some(contact_identifier) = payload.contact_identifier {
        if contact_identifier.starts_with("custom-") {
            if let Ok(contact_identifier) = contact_identifier.parse::<u64>() {
                let mut conn = match state
                    .database_connections
                    .get_timeout(Duration::from_millis(ENV.database_pool_timeout_in_millis))
                {
                    Ok(conn) => conn,
                    Err(why) => {
                        error!(
                            "unable to fetch database connection for request to transport message to discord: {:?}",
                            why
                        );

                        return;
                    }
                };

                let user_id =
                    trengo::utils::contract_id::to_snowflake(&contact_identifier.to_string());

                info!(
                    "recv for transport of message to discord ticket channel for user with id: {}",
                    user_id
                );

                let mut ticket = match TICKETS.fetch(&mut conn, user_id.into()) {
                    Some(ticket) => ticket,
                    None => {
                        warn!(
                            "unavailable ticket for user with id: {} for transport of message to discord ticket channel",
                            user_id
                        );

                        return;
                    }
                };

                let guild = match state
                    .discord_http
                    .get_guild(GuildId::new(ticket.guild_id.into()))
                    .await
                    .ok()
                {
                    Some(guild) => guild,
                    None => {
                        warn!(
                            "unavailable guild with id: {} for transport of message to discord ticket channel",
                            ticket.guild_id
                        );

                        return;
                    }
                };

                if let Some(mut channels) = guild.channels(&state.discord_http).await.ok() {
                    if !channels.contains_key(&ChannelId::new(ticket.channel_id.into())) {
                        let channel = tickets::create_channel(
                            &state.discord_http,
                            guild.id.into(),
                            ticket.user_id,
                            ticket.category_id,
                            &ticket.contact_name,
                        )
                        .await;

                        if channel.is_none() {
                            error!(
                                "error ticket channel for user with id: {} was not created for request to transport message to discord",
                                ticket.user_id
                            );

                            return;
                        }

                        let new_channel = channel.unwrap();

                        TICKETS.update(
                            &mut conn,
                            ticket.user_id,
                            TicketChangeset {
                                channel_id: Some(new_channel.id.get().into()),
                                ..Default::default()
                            },
                        );

                        ticket.channel_id = new_channel.id.get().into();

                        channels.insert(new_channel.id, new_channel);
                    }

                    let channel = match channels.get(&ticket.channel_id.into()) {
                        Some(channel) => channel,
                        None => {
                            error!(
                                "error ticket channel for user with id: {} was not found for request to transport message to discord",
                                ticket.user_id
                            );

                            return;
                        }
                    };

                    match trengo::fetch_message(payload.ticket_id, payload.message_id).await {
                        Some(message) => {
                            let attachments =
                                join_all(message.attachment_urls.into_iter().map(async |url| {
                                    CreateAttachment::url(&state.discord_http, &url).await
                                }))
                                .await
                                .into_iter()
                                .filter(|attachment| attachment.is_ok())
                                .map(|attachment| attachment.unwrap())
                                .collect::<Vec<_>>();

                            let builder = CreateMessage::new()
                                .content(message.content)
                                .add_files(attachments);

                            match channel.send_message(&state.discord_http, builder).await {
                                Ok(message) => {
                                    info!(
                                        "handled message transport to discord for user with id: {} successfully, message: {:?}",
                                        ticket.user_id, message
                                    );
                                }
                                Err(why) => {
                                    error!(
                                        "error encountered while transporting message to discord for user with id: {}: {:?}",
                                        ticket.user_id, why
                                    );
                                }
                            }
                        }
                        None => {
                            error!(
                                "error encountered while fetching message from trengo for transport to discord, user with id: {}",
                                ticket.user_id
                            );
                        }
                    }
                }
            } else {
                error!("error encountered while parsing contact identifier from webhook payload");
            }
        }
    }
}
