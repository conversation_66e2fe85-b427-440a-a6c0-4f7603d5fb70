use diesel::{
    SqliteConnection,
    r2d2::{self, ConnectionManager},
};
use dotenv::dotenv;
use rocket::routes;
use serenity::{all::Activity, prelude::*};
use std::sync::Arc;
use ttx_integration::{ENV, interactions, routes, trengo::RocketState};

#[tokio::main]
async fn main() {
    dotenv().ok();

    tracing_subscriber::fmt::init();

    let database_connections_manager =
        ConnectionManager::<SqliteConnection>::new(&ENV.database_url);

    let database_connections_pool = Arc::new(
        r2d2::Pool::builder()
            .min_idle(ENV.database_pool_size)
            .max_size(ENV.database_pool_size)
            .build(database_connections_manager)
            .expect("failed to create database connection pool"),
    );

    let token = &ENV.discord_token;

    let intents = GatewayIntents::GUILDS
        | GatewayIntents::GUILD_MESSAGES
        | GatewayIntents::GUILD_MEMBERS
        | GatewayIntents::MESSAGE_CONTENT;

    let mut discord_client = Client::builder(token, intents)
        .event_handler(interactions::on_message::Handler)
        .event_handler(interactions::on_ticket_create_button_press::Handler)
        .event_handler(interactions::on_command_interaction::Handler)
        .await
        .expect("failed to create discord client");

    let rocket_server = rocket::build()
        .manage(RocketState {
            discord_http: discord_client.http.clone(),
            database_connections: database_connections_pool.clone(),
        })
        .mount("/webhook", routes![routes::webhook::handler]);

    let (client_connection_result, server_lunch_result) =
        tokio::join!(discord_client.start(), rocket_server.launch());

    if let Err(why) = client_connection_result {
        eprintln!("error while connecting to discord: {:?}", why);
    }

    if let Err(why) = server_lunch_result {
        eprintln!("error while launching rocket server: {:?}", why);
    }
}
