use serenity::all::Channel;
use serenity::all::ComponentInteractionDataKind;
use serenity::all::CreateMessage;
use serenity::all::Interaction;
use serenity::all::MessageFlags;
use serenity::async_trait;
use serenity::prelude::*;
use tracing::error;
use tracing::info;
use tracing::warn;

use crate::components::TICKET_CREATE_BUTTON_ID;
use crate::components::TICKET_CREATION_CONFIRMATION_MESSAGE;
use crate::components::TICKET_EXISTS_MESSAGE;
use crate::components::support_description_embed;
use crate::tickets;
use crate::tickets::TICKETS;
use crate::tickets::models::TicketChangeset;
use crate::tickets::models::TicketInsert;
use crate::trengo;

pub struct Handler;

#[async_trait]
impl EventHandler for Handler {
    async fn interaction_create(&self, ctx: Context, interaction: Interaction) {
        if let Interaction::Component(component) = &interaction {
            if let ComponentInteractionDataKind::Button = component.data.kind {
                if component.data.custom_id.as_str() != TICKET_CREATE_BUTTON_ID {
                    return;
                }

                info!("recv ticket creation request");

                let guild_id = match component.guild_id {
                    Some(guild_id) => guild_id,
                    None => {
                        warn!("unavailable guild id for received ticket creation request");

                        return;
                    }
                };

                let category_id = {
                    let guild = match ctx.cache.guild(guild_id) {
                        Some(guild) => guild,
                        None => {
                            warn!(
                                "unavailable guild with id: {} for received ticket request",
                                guild_id
                            );

                            return;
                        }
                    };

                    let channel = match guild.channels.get(&component.channel_id) {
                        Some(channel) => channel,
                        None => {
                            warn!(
                                "unavailable channel with id: {} for received ticket creation request",
                                component.channel_id
                            );

                            return;
                        }
                    };

                    match channel.parent_id {
                        Some(parent_id) => parent_id,
                        None => {
                            warn!(
                                "unavailable parent id for channel with id: {} for received ticket creation request",
                                component.channel_id
                            );

                            return;
                        }
                    }
                };

                let interaction_channel = {
                    let channel = match ctx.http.get_channel(component.channel_id).await.ok() {
                        Some(channel) => match channel {
                            Channel::Guild(channel) => channel,
                            _ => {
                                warn!(
                                    "unavailable channel with id: {} for received ticket creation request",
                                    component.channel_id
                                );

                                return;
                            }
                        },
                        None => {
                            warn!(
                                "unavailable channel with id: {} for received ticket creation request",
                                component.channel_id
                            );

                            return;
                        }
                    };

                    channel
                };

                match TICKETS.fetch(&mut conn, component.user.id.get().into()) {
                    Some(mut ticket) => {
                        match ctx.http.get_channel(ticket.channel_id.into()).await.ok() {
                            Some(channel) => match channel {
                                Channel::Guild(_) => {
                                    interaction_channel
                                        .send_message(
                                            &ctx.http,
                                            CreateMessage::new()
                                                .content(TICKET_EXISTS_MESSAGE)
                                                .flags(MessageFlags::EPHEMERAL),
                                        )
                                        .await
                                        .ok();
                                }
                                _ => {
                                    warn!(
                                        "unavailable channel with id: {} for received ticket creation request",
                                        component.channel_id
                                    );

                                    return;
                                }
                            },
                            None => match tickets::create_channel(
                                &ctx.http,
                                guild_id.into(),
                                component.user.id.into(),
                                category_id.into(),
                                &component.user.id.to_string(),
                            )
                            .await
                            {
                                Some(ticket_channel) => {
                                    TICKETS.update(
                                        &mut conn,
                                        ticket.user_id,
                                        TicketChangeset {
                                            channel_id: Some(ticket_channel.id.get().into()),
                                            ..Default::default()
                                        },
                                    );

                                    ticket.channel_id = ticket_channel.id.get().into();

                                    ticket_channel
                                        .send_message(
                                            &ctx.http,
                                            CreateMessage::new().embed(support_description_embed()),
                                        )
                                        .await
                                        .ok();

                                    interaction_channel
                                        .send_message(
                                            &ctx.http,
                                            CreateMessage::new()
                                                .content(TICKET_CREATION_CONFIRMATION_MESSAGE)
                                                .flags(MessageFlags::EPHEMERAL),
                                        )
                                        .await
                                        .ok();
                                }
                                None => {
                                    error!(
                                        "error encountered while creating ticket channel for user with id: {}",
                                        component.user.id.get()
                                    );

                                    return;
                                }
                            },
                        };
                    }
                    None => {
                        let ticket_channel = match tickets::create_channel(
                            &ctx.http,
                            guild_id.into(),
                            component.user.id.into(),
                            category_id.into(),
                            &component.user.id.to_string(),
                        )
                        .await
                        {
                            Some(channel) => channel,
                            None => {
                                error!(
                                    "unavailable newly created ticket channel for user with id: {}",
                                    component.user.id.get()
                                );

                                return;
                            }
                        };

                        TICKETS.insert(
                            &mut conn,
                            TicketInsert {
                                user_id: component.user.id.get().into(),
                                guild_id: guild_id.get().into(),
                                category_id: category_id.get().into(),
                                channel_id: ticket_channel.id.get().into(),
                                contact_name: component.user.name.clone(),
                                contact_id: trengo::utils::contract_id::from_snowflake(
                                    component.user.id.into(),
                                ),
                            },
                        );

                        ticket_channel
                            .send_message(
                                &ctx.http,
                                CreateMessage::new().embed(support_description_embed()),
                            )
                            .await
                            .ok();

                        interaction_channel
                            .send_message(
                                &ctx.http,
                                CreateMessage::new()
                                    .content(TICKET_CREATION_CONFIRMATION_MESSAGE)
                                    .flags(MessageFlags::EPHEMERAL),
                            )
                            .await
                            .ok();
                    }
                }

                info!(
                    "ticket creation request handled successfully for user with id: {}",
                    component.user.id.get()
                );
            }
        }
    }
}
