pub mod contract_id {
    use crate::models::Snowflake;

    use super::base62;

    const TRENGO_CONTACT_ID_LIMIT: usize = 20;
    const PREFIX: &str = "custom-";
    const POSTFIX_CHAR: &str = "-";

    pub fn from_snowflake(snowflake: Snowflake) -> String {
        let encoded_id = base62::encode(snowflake.get());

        let needed = TRENGO_CONTACT_ID_LIMIT - encoded_id.len() - PREFIX.len();

        if needed > 0 {
            format!("{PREFIX}{encoded_id}{}", POSTFIX_CHAR.repeat(needed))
        } else {
            format!("{PREFIX}{encoded_id}")
        }
    }

    pub fn to_snowflake(contract_id: &str) -> Snowflake {
        let id = contract_id
            .trim_start_matches(PREFIX)
            .trim_end_matches(POSTFIX_CHAR);

        base62::decode(id).unwrap().into()
    }
}

mod base62 {
    const CHARS: &[u8] = b"0123456789abcde<PERSON><PERSON>ijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
    const BASE: usize = CHARS.len();
    const ZERO: usize = 0;

    /// encodes a given snowflake id to base62.
    pub fn encode(id: u64) -> String {
        let mut n = id as usize;
        let mut result = String::new();

        while n > ZERO {
            result.push(CHARS[n % BASE] as char);
            n /= BASE;
        }

        result
    }

    /// decodes a given base62 id to a snowflake.
    pub fn decode(id: &str) -> Option<u64> {
        if id.is_empty() {
            return None;
        }

        let mut result = ZERO;

        for c in id.chars() {
            match CHARS.iter().position(|&ch| ch as char == c) {
                Some(index) => result = result * BASE + index,
                None => return None,
            }
        }

        Some(result as u64)
    }
}
