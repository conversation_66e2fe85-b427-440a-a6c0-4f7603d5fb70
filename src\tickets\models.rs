use diesel::prelude::*;

use crate::models::Snow<PERSON>lake;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, De<PERSON>ult)]
pub struct Ticket {
    pub id: i32,
    pub user_id: Snowflake,
    pub guild_id: Snowflake,
    pub category_id: Snowflake,
    pub channel_id: Snowflake,
    pub contact_id: String,
    pub contact_name: String,
}

#[derive(Debug, Clone, Queryable, Selectable)]
#[diesel(table_name = super::schema::tickets)]
pub struct TicketDb {
    pub id: i32,
    pub user_id: String,
    pub guild_id: String,
    pub category_id: String,
    pub channel_id: String,
    pub contact_id: String,
    pub contact_name: String,
}

impl From<Ticket> for TicketDb {
    fn from(ticket: Ticket) -> Self {
        Self {
            id: ticket.id,
            user_id: ticket.user_id.to_string(),
            guild_id: ticket.guild_id.to_string(),
            category_id: ticket.category_id.to_string(),
            channel_id: ticket.channel_id.to_string(),
            contact_id: ticket.contact_id,
            contact_name: ticket.contact_name,
        }
    }
}

impl From<TicketDb> for Ticket {
    fn from(ticket: TicketDb) -> Self {
        Self {
            id: ticket.id,
            user_id: ticket.user_id.parse().unwrap(),
            guild_id: ticket.guild_id.parse().unwrap(),
            category_id: ticket.category_id.parse().unwrap(),
            channel_id: ticket.channel_id.parse().unwrap(),
            contact_id: ticket.contact_id,
            contact_name: ticket.contact_name,
        }
    }
}

#[derive(Debug, Clone, Default)]
pub struct TicketInsert {
    pub user_id: Snowflake,
    pub guild_id: Snowflake,
    pub category_id: Snowflake,
    pub channel_id: Snowflake,
    pub contact_id: String,
    pub contact_name: String,
}

#[derive(Debug, Clone, Insertable)]
#[diesel(table_name = super::schema::tickets)]
pub struct TicketInsertDb {
    pub user_id: String,
    pub guild_id: String,
    pub category_id: String,
    pub channel_id: String,
    pub contact_id: String,
    pub contact_name: String,
}

impl From<TicketInsert> for TicketInsertDb {
    fn from(ticket: TicketInsert) -> Self {
        Self {
            user_id: ticket.user_id.to_string(),
            guild_id: ticket.guild_id.to_string(),
            category_id: ticket.category_id.to_string(),
            channel_id: ticket.channel_id.to_string(),
            contact_id: ticket.contact_id,
            contact_name: ticket.contact_name,
        }
    }
}

impl From<TicketInsertDb> for TicketInsert {
    fn from(ticket: TicketInsertDb) -> Self {
        Self {
            user_id: ticket.user_id.parse().unwrap(),
            guild_id: ticket.guild_id.parse().unwrap(),
            category_id: ticket.category_id.parse().unwrap(),
            channel_id: ticket.channel_id.parse().unwrap(),
            contact_id: ticket.contact_id,
            contact_name: ticket.contact_name,
        }
    }
}

#[derive(Debug, Clone, Default)]
pub struct TicketChangeset {
    pub guild_id: Option<Snowflake>,
    pub category_id: Option<Snowflake>,
    pub channel_id: Option<Snowflake>,
    pub contact_name: Option<String>,
}

#[derive(Debug, Clone, AsChangeset)]
#[diesel(table_name = super::schema::tickets)]
pub struct TicketChangesetDb {
    pub guild_id: Option<String>,
    pub category_id: Option<String>,
    pub channel_id: Option<String>,
    pub contact_name: Option<String>,
}

impl From<TicketChangeset> for TicketChangesetDb {
    fn from(changeset: TicketChangeset) -> Self {
        Self {
            guild_id: changeset.guild_id.map(|id| id.to_string()),
            category_id: changeset.category_id.map(|id| id.to_string()),
            channel_id: changeset.channel_id.map(|id| id.to_string()),
            contact_name: changeset.contact_name,
        }
    }
}

impl From<TicketChangesetDb> for TicketChangeset {
    fn from(changeset: TicketChangesetDb) -> Self {
        Self {
            guild_id: changeset.guild_id.map(|id| id.parse().unwrap()),
            category_id: changeset.category_id.map(|id| id.parse().unwrap()),
            channel_id: changeset.channel_id.map(|id| id.parse().unwrap()),
            contact_name: changeset.contact_name,
        }
    }
}
